buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.7.3'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}
apply plugin: 'com.android.library'

android {
    namespace 'com.shockwave.pdfium'
    compileSdk 35
    ndkVersion "26.1.10909125"

    buildFeatures {
        buildConfig true
    }

    externalNativeBuild {
        ndkBuild {
            // Provides a relative path to your Android.mk build script.
            path "src/main/jni/Android.mk"
        }
    }

    packagingOptions {
        pickFirst 'lib/armeabi-v7a/libc++_shared.so'
        pickFirst 'lib/arm64-v8a/libc++_shared.so'
        pickFirst 'lib/x86_64/libc++_shared.so'
        pickFirst 'lib/x86/libc++_shared.so'

        pickFirst 'lib/armeabi-v7a/libjniPdfium.so'
        pickFirst 'lib/arm64-v8a/libjniPdfium.so'
        pickFirst 'lib/x86_64/libjniPdfium.so'
        pickFirst 'lib/x86/libjniPdfium.so'

        pickFirst 'lib/armeabi-v7a/libmodpdfium.so'
        pickFirst 'lib/arm64-v8a/libmodpdfium.so'
        pickFirst 'lib/x86_64/libmodpdfium.so'
        pickFirst 'lib/x86/libmodpdfium.so'
    }

    defaultConfig {
        minSdk 21
        targetSdk 35
        versionCode 1
        versionName "1.9.0"

        buildConfigField("String", "LIBRARY_VERSION_NAME", "\"${defaultConfig.versionName}\"")

    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

}



dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
}

